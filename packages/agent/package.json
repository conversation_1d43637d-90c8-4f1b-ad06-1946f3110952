{"name": "@packages/agent", "version": "0.1.0", "description": "ai agent for ever works", "private": true, "license": "UNLICENSED", "exports": {"./ai": {"types": "./dist/ai/index.d.ts", "default": "./dist/ai/index.js"}, "./data-generator": {"types": "./dist/data-generator/index.d.ts", "default": "./dist/data-generator/index.js"}, "./database": {"types": "./dist/database/index.d.ts", "default": "./dist/database/index.js"}, "./deploy": {"types": "./dist/deploy/index.d.ts", "default": "./dist/deploy/index.js"}, "./dto": {"types": "./dist/dto/index.d.ts", "default": "./dist/dto/index.js"}, "./entities": {"types": "./dist/entities/index.d.ts", "default": "./dist/entities/index.js"}, "./git": {"types": "./dist/git/index.d.ts", "default": "./dist/git/index.js"}, "./items-generator": {"types": "./dist/items-generator/index.d.ts", "default": "./dist/items-generator/index.js"}, "./markdown-generator": {"types": "./dist/markdown-generator/index.d.ts", "default": "./dist/markdown-generator/index.js"}, "./website-generator": {"types": "./dist/website-generator/index.d.ts", "default": "./dist/website-generator/index.js"}, "./services": {"types": "./dist/services/index.d.ts", "default": "./dist/services/index.js"}, "./config": {"types": "./dist/config/index.d.ts", "default": "./dist/config/index.js"}}, "scripts": {"dev": "concurrently \"npm:build:dev\" \"npm:build:types:dev\"", "build": "nest build -b swc && tsc --emitDeclarationOnly --declarationMap true", "build:dev": "nest build -b swc --watch", "build:types": "tsc --emitDeclarationOnly --declarationMap true", "build:types:dev": "tsc --emitDeclarationOnly --declarationMap true --watch"}, "peerDependencies": {"@nestjs/common": "*", "@nestjs/core": "*", "@nestjs/platform-express": "*", "reflect-metadata": "*", "rxjs": "*", "better-sqlite3": "^12.2", "pg": "^8.16", "mysql2": "^3.14"}, "peerDependenciesMeta": {"better-sqlite3": {"optional": true}, "pg": {"optional": true}, "mysql2": {"optional": true}}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@swc/cli": "^0.6.0", "@swc/core": "^1.12.9", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/node": "^22.16.0", "@types/supertest": "^6.0.3", "concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.3.0", "jest": "^29.7.0", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}, "dependencies": {"@langchain/anthropic": "^0.3.24", "@langchain/core": "^0.3.62", "@langchain/deepseek": "^0.0.1", "@langchain/groq": "^0.2.3", "@langchain/langgraph": "^0.2.74", "@langchain/mistralai": "^0.2.1", "@langchain/openai": "0.5.12", "@langchain/textsplitters": "^0.1.0", "@nestjs/bullmq": "^10.2.3", "@nestjs/config": "^4.0.2", "@nestjs/typeorm": "^11.0.0", "@supabase/supabase-js": "^2.50.3", "@tavily/core": "^0.3.7", "@types/cheerio": "^1.0.0", "@types/fs-extra": "^11.0.4", "@types/string-similarity": "^4.0.2", "axios": "^1.12.2", "bullmq": "^5.56.1", "cheerio": "^1.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "google-sr": "^5.0.0", "isomorphic-git": "^1.32.1", "langchain": "^0.3.29", "libsodium-wrappers": "^0.7.15", "octokit": "^3.2.2", "remark-stringify": "^11.0.0", "slugify": "^1.6.6", "string-similarity": "^4.0.4", "turndown": "^7.2.0", "typeorm": "^0.3.25", "unified": "^11.0.5", "yaml": "^2.8.0", "zod": "^3.25.73"}}