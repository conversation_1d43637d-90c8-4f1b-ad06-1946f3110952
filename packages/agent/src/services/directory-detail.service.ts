// This service will have a method that receives name and prompt then extracts description, keywords, etc.
// And slug which should be generated
import { Injectable } from '@nestjs/common';
import { AiService, BaseChatModel } from '../ai';
import { User } from '../entities';
import { DirectoryRepository } from '../database';

@Injectable()
export class DirectoryDetailService {
    private llm: BaseChatModel;

    constructor(
        private readonly aiService: AiService,
        private readonly directoryRepository: DirectoryRepository,
    ) {
        this.llm = this.aiService.createLlmWithTemperature(0.0);
    }

    async extractDetails(name: string, prompt: string, user: User) {}
}
